services:
	- App\AdminModule\Presenters\AdminAccessChecker(
		%admin.allowedIpRanges%
	)

	- App\Model\AdminEmails(
		adminEmailDomains: %emailDomains.admin%
		developerEmailDomains: %emailDomains.developer%
	)

parameters:
	admin:
		allowedIpRanges: []
			#- "***********/16"
			#- "127.0.0.1"
			#- "::1"

	emailDomains:
		admin: []
		developer: []

	config:
		adminMenu:
			Modules:
				- {title: Library, resource: Admin:Library, action: :Admin:Library:default, icon: file}
				- {title: Pages, resource: Page:Admin:Page, action: :Page:Admin:Page:default, icon: file}
				- {title: Products, resource: Admin:Catalog, action: :Admin:Catalog:default, icon: file}
				- {title: ProductVariants, resource: Admin:ProductVariant, action: :Admin:ProductVariant:default, icon: file, sub: true}
				- {title: Blog, resource: Blog:Admin:Blog, action: :Blog:Admin:Blog:default, icon: file}
				- {title: BlogTag, resource: BlogTag:Admin:BlogTag, action: :BlogTag:Admin:BlogTag:default, icon: tag, sub: true}
				- {title: Author, resource: Author:Admin:Author, action: :Author:Admin:Author:default, icon: file, sub: true}
				- {title: Calendar, resource: Calendar:Admin:Calendar, action: :Calendar:Admin:Calendar:default, icon: calendar}
				- {title: CalendarTag, resource: CalendarTag:Admin:CalendarTag, action: :CalendarTag:Admin:CalendarTag:default, icon: tag, sub: true}
				- {title: Banner, resource: Banner:Admin:Banner, action: :Banner:Admin:Banner:default, icon: file}
				- {title: Discount, resource: Discount:Admin:Discount, action: :Discount:Admin:Discount:default, icon: file}
				- {title: MenuMain, resource: MenuMain:Admin:MenuMain, action: :MenuMain:Admin:MenuMain:default, icon: file}
#				- {title: Supplier, resource: Supplier:Admin:Supplier, action: :Supplier:Admin:Supplier:default, icon: file}
				- {title: ProductReview, resource: ProductReview:Admin:ProductReview, action: :ProductReview:Admin:ProductReview:default, icon: file}
#				- {title: Holidays, resource: Admin:Holiday, action: :Admin:Holiday:default, icon: file}
				- {title: Orders, resource: Admin:Order, action: :Admin:Order:default, icon: file}
#				- {title: Tag, resource: Tag:Admin:Tag, action: :Tag:Admin:Tag:default, icon: tags}
#				- {title: Promotion, resource: Promotion:Admin:Promotion, action: :Promotion:Admin:Promotion:default, icon: percent}
				- {title: Gift, resource: Gift:Admin:Gift, action: :Gift:Admin:Gift:default, icon: gifts}
				- {title: ContactMessage, resource: ContactMessage:Admin:ContactMessage, action: :ContactMessage:Admin:ContactMessage:default, icon: file}

			Lists: # veci které se plní frontenedem
				- {title: E-mails, resource: Admin:Newsletter, action: :Admin:Newsletter:default, icon: file}

			Settings:
				- {title: Transports, resource: Admin:DeliveryMethod, action: :Admin:DeliveryMethod:default, icon: file}
				- {title: Payments, resource: Admin:PaymentMethod, action: :Admin:PaymentMethod:default, icon: file}
				- {title: Vouchers, resource: Admin:Voucher, action: :Admin:Voucher:default, icon: file}
				- {title: System strings, resource: Admin:String, action: :Admin:String:default, icon: file}
				- {title: Mutations, resource: Admin:Mutation, action: :Admin:Mutation:default, icon: file}
				- {title: States, resource: Admin:State, action: :Admin:State:default, icon: file}
				- {title: Parameters, resource: Admin:Parameter, action: :Admin:Parameter:default, icon: ruler-combined}
				- {title: Users, resource: Admin:User, action: :Admin:User:default, icon: users}
				- {title: E-mails template, resource: Admin:Email, action: :Admin:Email:default, icon: file}
				- {title: Redirects, resource: Admin:Redirect, action: :Admin:Redirect:default, icon: file}
				- {title: system_messages, resource: SystemMessage:Admin:SystemMessage, action: :SystemMessage:Admin:SystemMessage:default, icon: file}
			-
				- {title: Synchronizace, resource: Admin:Sync, action: :Admin:Sync:default, icon: sync-alt} #vidi jen developeri
				- {title: Elastic search, resource: Admin:Elastic, action: :Admin:Elastic:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: Cache, resource: Admin:Cache, action: :Admin:Cache:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: API tokens, resource: Admin:ApiToken, action: :Admin:ApiToken:default, icon: file, devOnly: true}
				- {title: Help, resource: superadmin, action: :Admin:Help:default, icon: file}
				- {title: About, resource: superadmin, action: :Admin:Help:about, icon: file}

		modules:
			Admin:Homepage: Homepage
			Page:Admin:Page: Page
			Blog:Admin:Blog: Blog
			BlogTag:Admin:BlogTag: BlogTag
			Calendar:Admin:Calendar: Calendar
			CalendarTag:Admin:CalendarTag: CalendarTag
			Banner:Admin:Banner: Banner
			Author:Admin:Author: Author
			Discount:Admin:Discount: Discount
			Tag:Admin:Tag: Tag
			Admin:Library: Library
			Admin:File: File
			Admin:Catalog: Catalog
			Admin:ProductVariant: ProductVariant
			Admin:Product: Products
			Admin:Class: Classes
			Admin:ClassEvent: Class event
			Admin:ClassSection: Class section
			Admin:User: Users
			Admin:Parameter: Parameters
			Admin:Email: E-mails
			Admin:String: System strings
			Admin:Developer: Developerské nastavení
			Admin:Help: Help
			Admin:Newsletter: Newsletter
			Admin:Place: Place
			Admin:Search: Search
			Admin:Search2: Search
			Admin:Template: Templates
			Admin:Redirect: Redirect
			Admin:Mutation: Mutation
			Admin:Elastic: Elastic
			Admin:State: State
			Admin:Styleguide: Styleguide
			Admin:Cache: Cache
			Admin:Sync: Sync
			Admin:ApiToken: API tokeny
			SystemMessage:Admin:SystemMessage: SystemMessage
			Supplier:Admin:Supplier: Supplier
			MenuMain:Admin:MenuMain: MenuMain
			ProductReview:Admin:ProductReview: ProductReview
			Admin:Holiday: Holiday
			Admin:Order: Order
			Admin:DeliveryMethod: Transport
			Admin:PaymentMethod: Payment
			Admin:Voucher: Voucher
			Promotion:Admin:Promotion: Promotion
			Gift:Admin:Gift: Gift
			UserAnimal:Admin:UserAnimal: UserAnimal
			ContactMessage:Admin:ContactMessage: ContactMessage

		adminPaging: 20
		adminImagePaging: 32
		productPaging: 20

		tabs:
			pages: [
				images,
				videos,
				files,
				linkedCategories,
				seo,
				settings,
			]
			products: [
				variants,
				content,
				images,
				files,
				params,
				links,
				videos,
				reviews,
				articlePages,
				accessories, # doporucujeme
				presents,
				similar, #podobne produkty
				product, #podobne produkty pro vyprodany produkt
				contents,
				seo,
				settings,
			]
			emailTemplates: [
				files,
			]

		tabsHideOnlyOnTemplates:
			pages:
#				files:
#				links:
#				videos:
				attproducts:
#					- Article:detail
#					- Article:default
				pages:
#					- Article:detail
#					- Article:default
				faqs:
#					- Article:detail
#					- Article:default
				reviews:
#					- Article:detail
#					- Article:default
				attproductsReview:
#					- Article:detail
#					- Article:default



		tabsShowOnlyOnTemplates: # note: tabs are defined in admin.neon
			pages:
				params:
#					- Article:detail
				linkedCategories:
					- Catalog:default
#				faqs:
#				reviews:

includes:
	- lang/lang.neon
