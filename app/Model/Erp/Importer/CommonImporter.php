<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\ConfigService;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Connector\ReadCollection;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Erp\Traits\HasLogger;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\ImportCache\ImportCacheMapper;
use App\Model\Orm\ImportCacheTime\ImportCacheTimeModel;
use Contributte\Monolog\LoggerManager;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Nette\Utils\FileSystem;
use Nextras\Dbal\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

abstract class CommonImporter
{

	use HasLogger;

	protected ArrayHash $config;

	protected LoggerInterface $logger;

	public function __construct(
		protected readonly ReadCollection $commonConnector,
		protected readonly ImportCacheMapper $importCacheMapper,
		protected readonly ConfigService $configService,
		protected readonly LoggerManager $loggerManager,
		protected readonly Connection $connection,
		protected readonly MessageBusInterface $messageBus,
		protected readonly ImportCacheTimeModel $importCacheTimeModel,
	)
	{
		$this->config = ArrayHash::from($this->configService->get('erp'));
	}

	abstract public function import(string $importType, ?Query $query = null): Result;

	protected function getLogger(string $importType): LoggerInterface
	{
		return $this->getLoggerByImportType($this->loggerManager, $importType);
	}

	protected function getDataSource(?Query $query = null): ArrayHash
	{
		$result = $this->commonConnector->getRows($query);

		if ($result->count() === 0) {
			$msg = sprintf('Result from %s is empty.', $this->commonConnector::class);
			$this->logger->error($msg);
			throw new InvalidStateException($msg);
		}

		return $result;
	}

	/** @noinspection PhpUnusedParameterInspection */
	protected function getJsonSource(?Query $query = null): string
	{
		throw new InvalidStateException('JSON source file is not supported.');
	}

	protected function flushOldImports(string $importType): void
	{
		$deleted = $this->importCacheMapper->deleteByStatus([
			ImportCache::STATUS_IMPORTED,
			ImportCache::STATUS_SKIPPED,
		], $importType);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);
	}

	protected function flushImportFile(string $source): void
	{
		try {
			FileSystem::delete($source);
			$this->logger->info('JSON file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('JSON file could not be deleted.', ['exception' => $e]);
		}
	}

}
