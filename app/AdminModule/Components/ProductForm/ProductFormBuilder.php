<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductForm;

use App\AdminModule\Components\ProductForm\Section\Tag\TagBuilder;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\State\State;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Stock\Stock;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\Model\Translator;
use App\PostType\Tag\Model\TagType;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\ICollection;

final class ProductFormBuilder
{

	/** @var ICollection<State> */
	private ICollection $states;

	/** @var ICollection<PriceLevel> */
	private ICollection $priceLevels;

	/** @var ICollection<Stock> */
	private ICollection $stocks;


	public function __construct(
		private readonly Orm $orm,
		private readonly CatalogTreeModel $catalogTreeModel,
		private readonly Translator $translator,
		private readonly TagBuilder $tagBuilder,
	)
	{
		$this->states = $this->orm->state->findBy(['code' => [State::CODE_CZ, State::CODE_SK]]);
		$this->priceLevels = $this->orm->priceLevel->findBy(['type' => [PriceLevel::TYPE_PURCHASE, PriceLevel::TYPE_RECOMMENDED]]);
		$this->stocks = $this->orm->stock->findBy(['alias' => Stock::ALIAS_SHOP]);

	}


	/**
	 * @param ICollection<Mutation> $mutations
	 */
	public function build(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$this->addProductCommonToForm($form, $product, $mutations, $postData);
		$this->addProductLocalizationsToForm($form, $product, $mutations, $postData);
		$this->addVariantsToForm($form, $product, $mutations, $postData);

		$this->addButtonsCommon($form);
		$this->addButtonsMutation($form, $product, $mutations);
	}


	/**
	 * @param ICollection<Mutation> $mutations
	 */
	private function addProductCommonToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$container = $form->addContainer('productCommon');
		$container->addText('internalName', 'internalName')->setDefaultValue($product->internalName);
//		$container->addCheckbox('isOld', $this->translator->translate('is_old'))->setDefaultValue($product->isOld);
//		$container->addCheckbox('isNew', $this->translator->translate('is_new'))->setDefaultValue($product->hasTag(TagType::new))->setOmitted();
//		$container->addCheckbox('isBestseller', $this->translator->translate('is_bestseller'))->setDefaultValue($product->hasTag(TagType::bestseller))->setOmitted();
//		$container->addCheckbox('isTop', $this->translator->translate('is_top'))->setDefaultValue($product->hasTag(TagType::top))->setOmitted();
//		$container->addCheckbox('isInPrepare', $this->translator->translate('is_in_prepare'))->setDefaultValue($product->isInPrepare)->setOmitted();
		$container->addCheckbox('isElectronic', $this->translator->translate('is_electronic'))->setDefaultValue($product->isElectronic);
//		$container->addCheckbox('isDamaged', $this->translator->translate('is_damaged'))->setDefaultValue($product->isDamaged)->setOmitted();
		$container->addCheckbox('isFreeTransport', $this->translator->translate('is_free_transport'))->setDefaultValue($product->isFreeTransport);
//		$container->addCheckbox('isFreeTransportForced', $this->translator->translate('is_free_transport_forced'))->setDefaultValue($product->isFreeTransportForced);

//		$container->addDate('freeTransportForcedFrom', 'free_transport_forced_from')->setDefaultValue($product->freeTransportForcedFrom);
//		$container->addDate('freeTransportForcedTo', 'free_transport_forced_to')->setDefaultValue($product->freeTransportForcedTo);

//		$container->addCheckbox('notSoldSeparately', $this->translator->translate('not_sold_separately'))->setDefaultValue($product->notSoldSeparately);
		$container->addText('publicFrom', 'publicFrom')->setRequired();
		if ($product->publicFrom) {
			$container['publicFrom']->setDefaultValue($product->publicFrom->format('Y-m-d\TH:i'));
		}
		$container->addText('publicTo', 'publicTo')->setRequired();
		if ($product->publicTo) {
			$container['publicTo']->setDefaultValue($product->publicTo->format('Y-m-d\TH:i'));
		}

		$container->addSelect('template', 'template', [':Front:Product:detail' => 'Product'])->setDefaultValue(':Front:Product:detail');

		$this->addImages($container, $product, $mutations, $postData);

//		$this->addAccessories($container, $product, $postData);
		$this->addProducts($container, $product, $postData);
		$this->addSimilarProducts($container, $product, $postData);
	}


	/**
	 * @param ICollection<Mutation> $mutations
	 */
	private function addProductLocalizationsToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$container = $form->addContainer('productLocalizations');
		foreach ($mutations as $mutation) {
			$localization = $product->getLocalization($mutation);

			$localizationContainer = $container->addContainer($mutation->id);
			$localizationContainer->addText('name', $mutation->langCode . '_name')->setDefaultValue($localization->name);
			$localizationContainer->addText('nameTitle', $mutation->langCode . '_nameTitle')->setDefaultValue($localization->nameTitle);
			$localizationContainer->addText('nameAnchor', $mutation->langCode . '_nameAnchor')->setDefaultValue($localization->nameAnchor);

			$localizationContainer->addTextArea('annotation', $mutation->langCode . '_annotation')->setDefaultValue($localization->annotation);
			$localizationContainer->addTextArea('content', $mutation->langCode . '_content')->setDefaultValue($localization->content);
			$localizationContainer->addTextArea('description', $mutation->langCode . '_description')->setDefaultValue($localization->description);
			$localizationContainer->addTextArea('keywords', $mutation->langCode . '_keywords')->setDefaultValue($localization->keywords);

			$localizationContainer->addText('alias', $mutation->langCode . '_alias')->setDefaultValue($localization->getAlias());
			$localizationContainer->addTextArea('aliasHistory', $mutation->langCode . '_aliasHistory')->setDefaultValue($localization->getAliasHistoryString());

			$localizationContainer->addCheckbox('public', 'public')->setDefaultValue($localization->public);

			$this->addCategoriesToContainer($localizationContainer, $localization, $postData);
			$this->tagBuilder->addTagsToContainer($localizationContainer, $localization, $postData);
			$this->addPagesToContainer($localizationContainer, $localization, $postData);
			$this->addFilesToContainer($localizationContainer, $localization, $postData);

			$setupContainer = $localizationContainer->addContainer('setup');
			$setupContainer->addCheckbox('inheritCategories')->setDefaultValue(
				(isset($localization->setup->inheritCategories) && $localization->setup->inheritCategories)
			);

			$setupContainer->addCheckbox('inheritFiles')->setDefaultValue(
				(isset($localization->setup->inheritFiles) && $localization->setup->inheritFiles)
			);
			$setupContainer->addCheckbox('inheritPages')->setDefaultValue(
				(isset($localization->setup->inheritPages) && $localization->setup->inheritPages)
			);

			$localizationContainer->addHidden('cf');
			$localizationContainer->addHidden('cc');
			$localizationContainer->addHidden('ccScheme');

		}
	}


	/**
	 * @param ICollection<Mutation> $mutations
	 */
	private function addVariantsToForm(Form $form, Product $product, ICollection $mutations, array $postData): void
	{
		$variantsContainer = $form->addContainer('variants');

		$parametersForVariant = $this->orm->parameter->findByProductType($product->typeName, true);

		if (!$postData) {
			foreach ($product->variants as $variant) {
				$this->addVariantToForm($variantsContainer, $variant, $variant->id, $parametersForVariant);
			}
		} elseif (isset($postData['variants'])) {
			foreach ($postData['variants'] as $variantKey => $variantData) {
				if (is_int($variantKey)) {
					$fakeVariant = null;
					$this->addVariantToForm($variantsContainer, $fakeVariant, $variantKey, $parametersForVariant);
				} elseif (preg_match('/^newItemMarker_/', $variantKey)) {
					$variant = $product->variants->toCollection()->getById($variantKey);
					$this->addVariantToForm($variantsContainer, $variant, $variantKey, $parametersForVariant);
				}
			}
		}

		$fakeVariant = null;
		$this->addVariantToForm($variantsContainer, $fakeVariant, 'newItemMarker', $parametersForVariant);
	}


	private function addVariantCommonToForm(Container $container, ?ProductVariant $variant, iterable $parametersForVariant): void
	{
		$container = $container->addContainer('variantCommon');

		$container->addText('ean', 'ean');
		$container->addCheckbox('isOld', 'is_old');

		//$container->addText('code', 'code');

		if ($variant) {
			$container['ean']->setDefaultValue($variant->ean);
			$container['isOld']->setDefaultValue($variant->isOld);
//			$container['code']->setDefaultValue($variant->code);
		}
//
//		foreach ($parametersForVariant as $key => $parameter) {
//			/** @var Parameter $parameter */
//			$counter = $key + 1;
//			$parameterValueFunctionName = "getterParam{$counter}ValueId";
//			$inputOptions = $parameter->options->toCollection()->fetchPairs('id', 'internalValue');
//			$container->addSelect("param{$counter}Value", $parameter->uid, $inputOptions)
//				->setPrompt('');
//
//			if ($variant && in_array($variant->$parameterValueFunctionName(), array_keys($inputOptions))) {
//				$container["param{$counter}Value"]->setDefaultValue($variant->$parameterValueFunctionName());
//			}
//		}
	}


	private function addVariantLocalizationsToForm(Container $container, ?ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantLocalizations');

		foreach ($this->orm->mutation->findAll() as $mutation) {
			$mutationsContainer = $container->addContainer($mutation->id);
			$mutationsContainer->addCheckbox('active', $mutation->langCode . '_active');
			$mutationsContainer->addText('name', 'name_variant');
			$mutationsContainer->addText('nameShort', 'name_variant_short');

			if ($variant) {
				$variantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
				$mutationsContainer['active']->setDefaultValue($variantLocalization->active);
				$mutationsContainer['name']->setDefaultValue($variantLocalization->name);
				$mutationsContainer['nameShort']->setDefaultValue($variantLocalization->nameShort);
			}
		}
	}
	private function addVariantPricesToForm(Container $container, ?ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantPrices');

		foreach ($this->orm->mutation->findAll() as $mutation) {
			$mutationsContainer = $container->addContainer($mutation->id);
			$currency = $mutation->currency->getCurrencyCode();
			$currencyContainer = $mutationsContainer->addContainer($currency);
			foreach ($this->priceLevels as $priceLevel) {

				$priceLevelContainer = $currencyContainer->addContainer($priceLevel->id);
				$priceLevelContainer->addText('price', 'price')->setHtmlType('number')->setHtmlAttribute('step',
					0.0001);
				$priceLevelContainer->addText('vat', 'vat')->setHtmlType('number');
				$priceLevelContainer->addText('priceVat', 'priceVat')->setHtmlType('number')->setNullable()->setHtmlAttribute(
					'step',
					0.0001
				);
				if ($priceLevel->hasValid) {
					$priceLevelContainer->addText('validFrom',
						'valid_from')->setHtmlType('date')->setNullable(); //->setHtmlAttribute('readonly', true);
					$priceLevelContainer->addText('validTo',
						'valid_to')->setNullable();//->setHtmlAttribute('readonly', true);
				}

				$price = $variant?->prices->toCollection()->getBy([
					'mutation' => $mutation,
					'priceLevel' => $priceLevel,
				]);

				if ($variant !== null && $price !== null) {
					assert($price instanceof ProductVariantPrice);
					$priceAmount = round((float) $price->price->amount, 2);

					$priceLevelContainer['price']->setDefaultValue($priceAmount);
					$priceLevelContainer['vat']->setDefaultValue($price->vat);
					if ($priceLevel->hasValid) {
						$priceLevelContainer['validFrom']->setDefaultValue($price->validFrom?->format('Y-m-d\TH:i'));
						$priceLevelContainer['validTo']->setDefaultValue($price->validTo?->format('Y-m-d\TH:i'));
					}
				} else {
					$priceLevelContainer['price']->setDefaultValue(0);
					$priceLevelContainer['vat']->setDefaultValue(($mutation->getFirstState()?->vatRates->get(VatRate::Standard)->toFloat() ?? 21));
				}
			}
		}
	}

//	private function addVariantPricesToForm2(Container $container, ?ProductVariant $variant = null): void
//	{
//		$container = $container->addContainer('variantPrices');
//		/** @var Mutation $mutation */
//		foreach ($this->orm->mutation->findAll() as $mutation) {
//			$mutationsContainer = $container->addContainer($mutation->id);
//			//foreach (CurrencyHelper::CURRENCIES as $currency) {
//			$currency = $mutation->currency->getCurrencyCode();
//				$currencyContainer = $mutationsContainer->addContainer($currency);
//				foreach ($this->priceLevels as $priceLevel) {
//
//				}
//			//}
//		}
//	}

	private function addVariantSuppliesToForm(Container $container, ?ProductVariant $variant = null): void
	{
		$container = $container->addContainer('variantSupplies');
		foreach ($this->stocks as $stock) {
			$stockContainer = $container->addContainer($stock->id);
			$stockContainer->addText('amount', 'product_stock_amount');

			if ($variant && isset($variant->suppliesByStockEntity[$stock->id])) {
				$stockContainer['amount']->setDefaultValue($variant->suppliesByStockEntity[$stock->id]->amount);
			}
		}
	}


	/**
	 * @param ICollection<Mutation> $mutations
	 */
	private function addButtonsMutation(Form $form, Product $product, ICollection $mutations): void
	{
		$container = $form->addContainer('buttonsMutation');

		foreach ($mutations as $mutation) {
			$name = sprintf('%s%s', ProductForm::SUBMIT_MUTATION_REMOVE, Strings::firstUpper($mutation->langCode));
			$container->addSubmit($name, $name);
		}
	}


	private function addButtonsCommon(Form $form): void
	{
		$form->addSubmit('send');
//		$form->addSubmit('delete');
	}

	/**
	 * @param ICollection<Parameter> $parametersForVariant
	 */
	private function addVariantToForm(Container $variantsContainer, ?ProductVariant $variant, int|string $variantKey, ICollection $parametersForVariant): void
	{
		$variantContainer = $variantsContainer->addContainer($variantKey);
		$this->addVariantCommonToForm($variantContainer, $variant, $parametersForVariant);
		$this->addVariantLocalizationsToForm($variantContainer, $variant);
		$this->addVariantPricesToForm($variantContainer, $variant);
		$this->addVariantSuppliesToForm($variantContainer, $variant);
	}

	private function addFilesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$filesContainer = $localizationContainer->addContainer('files');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['files'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['files'] as $fileKey => $file) {
					$fileContainer = $filesContainer->addContainer($fileKey);
					$fileContainer->addText('fileName', 'fileName')->setDefaultValue($file['fileName']);
					$fileContainer->addHidden('fileId', $file['fileId']);
				}
			}
		} else {
			foreach ($localization->files as $fileKey => $productFile) {
				$fileContainer = $filesContainer->addContainer($fileKey);
				$fileContainer->addText('fileName', 'fileName')->setDefaultValue($productFile->name);
				$fileContainer->addHidden('fileId', $productFile->file->id);
			}
		}

		//fake
		$fileContainer = $filesContainer->addContainer('newItemMarker');
		$fileContainer->addText('fileName', 'fileName');
		$fileContainer->addHidden('fileId', '');
	}



	private function addPagesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$pagesContainer = $localizationContainer->addContainer('pages');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['pages'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['pages'] as $pageKey => $page) {
					$pageContainer = $pagesContainer->addContainer($pageKey);
					$pageContainer->addHidden('id');
					$pageContainer->addText('name', 'name');
				}
			}
		} else {
			foreach ($localization->pages as $pageKey => $page) {
				$pageContainer = $pagesContainer->addContainer($pageKey);
				$pageContainer->addHidden('id')->setDefaultValue($page->id);
				$pageContainer->addText('name', 'name')
					->setDefaultValue($page->name);

			}
		}

		//fake
		$pageContainer = $pagesContainer->addContainer('newItemMarker');
		$pageContainer->addHidden('id');
		$pageContainer->addText('name', 'name');
	}


	/**
	 * @param ICollection<Product> $collection
	 */
	private function addProductRelation(string $containerName, ICollection $collection, Container $container, Product $product, array $postData): void
	{
		$itemsContainer = $container->addContainer($containerName);

		if ($postData) {
			if (isset($postData['productCommon'][$containerName])) {
				foreach ($postData['productCommon'][$containerName] as $itemKey => $item) {
					$accessoryContainer = $itemsContainer->addContainer($itemKey);
					$accessoryContainer->addHidden('id');
					$accessoryContainer->addText('name');
				}
			}
		} else {
			foreach ($collection as $item) {
				$accessoryContainer = $itemsContainer->addContainer($item->id);
				$accessoryContainer->addHidden('id')->setDefaultValue($item->id);
				$accessoryContainer->addText('name')->setDefaultValue($item->internalName);
			}
		}

		//fake
		$accessoryContainer = $itemsContainer->addContainer('newItemMarker');
		$accessoryContainer->addHidden('id');
		$accessoryContainer->addText('name');
	}


//	private function addAccessories(Container $container, Product $product, array $postData): void
//	{
//		$containerName = 'accessories';
//		$collection = $product->accessoriesAll;
//		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
//	}


	private function addProducts(Container $container, Product $product, array $postData): void
	{
		$containerName = 'products';
		$collection = $product->productsAll;
		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
	}

	private function addSimilarProducts(Container $container, Product $product, array $postData): void
	{
		$containerName = 'similar';
		$collection = $product->similarProductsAll;
		$this->addProductRelation($containerName, $collection, $container, $product, $postData);
	}

	/**
	 * @param ICollection<Mutation> $mutations
	 */
	private function addImages(Container $container, Product $product, ICollection $mutations, array $postData): void
	{
		/** @var ICollection< Mutation> $mutations */

		$containerName = 'images';
		$imagesContainer = $container->addContainer($containerName);

		if ($postData) {
			if (isset($postData['productCommon'][$containerName])) {
				foreach ($postData['productCommon'][$containerName] as $imageKey => $item) {

					$imageContainer = $imagesContainer->addContainer($imageKey);
					$imageContainer->addHidden('imageId', $item['imageId']);
					$dataInp = [];
					foreach ($product->variants as $variant) {
						$dataInp[$variant->id] = 'Ean: ' . $variant->ean . ', ' . $this->translator->translate('code') . ': ' . $variant->code;
					}

					$imageContainer->addMultiSelect('variants', 'usedInVariants', $dataInp);
					foreach ($mutations as $mutation) {
						$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
						$imageNameContainer->addText('name', $mutation->langCode . '_name');
						if (isset($item[$mutation->langCode]['name'])) {
							$imageNameContainer['name']->setDefaultValue($item[$mutation->langCode]['name']);
						}
					}
				}
			}
		} else {
			foreach ($product->images as $imageKey => $productImage) {
				$imageContainer = $imagesContainer->addContainer($productImage->libraryImage->id);
				$imageContainer->addHidden('imageId', $productImage->libraryImage->id);
				$dataInp = [];
				foreach ($product->variants as $variant) {
					$dataInp[$variant->id] = 'Ean: ' . $variant->ean . ', ' . $this->translator->translate('erpCode') . ': ' . $variant->extId;
				}

				$imageContainer->addMultiSelect('variants', 'usedInVariants', $dataInp);
				if (!empty($productImage->getVariantIds()) && isset($productImage->getVariantIds()[0]) && $productImage->getVariantIds()[0] !== '') {
					$imageContainer['variants']->setDefaultValue($productImage->getVariantIds());
				}

				foreach ($mutations as $mutation) {

					$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
					$imageNameContainer->addText('name', $mutation->langCode . '_name');

					$langCode = $mutation->langCode;
					if (isset($productImage->data->$langCode->name)) {
						$imageNameContainer['name']->setDefaultValue($productImage->data->$langCode->name);
					}
				}
			}
		}

		// fake
		$imageContainer = $imagesContainer->addContainer('newItemMarker');
		$imageContainer->addHidden('imageId', '');
		$dataInp = [];
		foreach ($product->variants as $variant) {
			$dataInp[$variant->id] = 'Ean: ' . $variant->ean . ', ' . $this->translator->translate('code') . ': ' . $variant->code;
		}

		$imageContainer->addMultiSelect('variants', 'usedInVariants', $dataInp);
		foreach ($mutations as $mutation) {
			$imageNameContainer = $imageContainer->addContainer($mutation->langCode);
			$imageNameContainer->addText('name', $mutation->langCode . '_name');
			$langCode = $mutation->langCode;
//			if (isset($image->data->$langCode->name)) {
//				$imageNameContainer['name']->setDefaultValue($image->data->$langCode->name);
//			}
		}
	}



	private function addCategoriesToContainer(Container $localizationContainer, ProductLocalization $localization, array $postData): void
	{
		$categoriesContainer = $localizationContainer->addContainer('categories');
		if ($postData) {
			if (isset($postData['productLocalizations'][$localization->mutation->id]['categories'])) {
				foreach ($postData['productLocalizations'][$localization->mutation->id]['categories'] as $categoryKey => $category) {
					$categoryContainer = $categoriesContainer->addContainer($categoryKey);
					$categoryContainer->addHidden('id');
					$categoryContainer->addText('name', 'name');
				}
			}
		} else {

			$categories = $this->catalogTreeModel->findCategoriesForProductLocalization($localization);

			foreach ($categories as $categoryKey => $category) {
				$categoryContainer = $categoriesContainer->addContainer($categoryKey);
				$categoryContainer->addHidden('id', $category->id);
				$categoryContainer->addText('name', 'name')
					->setDefaultValue($category->getPathSentenceWithMyself());

			}
		}

		//fake
		$categoryContainer = $categoriesContainer->addContainer('newItemMarker');
		$categoryContainer->addHidden('id');
		$categoryContainer->addText('name', 'name');
	}



}
